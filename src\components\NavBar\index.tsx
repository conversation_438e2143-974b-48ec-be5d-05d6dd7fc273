import "./index.scss";

interface IProps {
  companyName: string;
  About: string;
  islogin: boolean;
  setIsLogin:(val:boolean)=>void
}

const NavBar = ({ companyName, About,islogin,setIsLogin}: IProps) => {

  return (
    <nav>
      <ul className="navbar-list">
        <li>
          <a href="/">{companyName}</a>
        </li>
        <li>
          <a href="/">Home</a>
        </li>
        <li>
          <a href="/">{About}</a>
        </li>
        <li>
          <a href="/">contact me</a>
        </li>
        <li>
          <button
            onClick={() => {
              setIsLogin(!islogin );
              console.log(islogin);
            }}
          >
            {islogin ? "Logout" : "Login"}
          </button>
        </li>
      </ul>
    </nav>
  );
};

export default NavBar;
