import type { IUserData } from "../../interfaces";
import "./index.scss";
import { formInputList } from './../data/index';



interface IProps {
  setIsLogin: (val: boolean) => void;
  userData:IUserData
  setUserData:(user:IUserData)=>void
}


const RegisterForm = ({ setIsLogin,userData,setUserData }: IProps) => {

// ** Handlers
const onChangeHandler =(e:React.ChangeEvent<HTMLInputElement>)=>{
  const {value,name}=e.target
setUserData({
  ...userData,
  [name]:value
})  
}
// ** renders
const renderInputForm = formInputList.map((item)=>{
  console.log(item)
  return (
   <div className="input-wrapper" key={item.id}>
          <label htmlFor={item.name}>{item.label}</label>
          <input
            type={item.type}
            name={item.name}
            value={userData[item.name]}
            onChange={onChangeHandler}
          />
        </div>
  )
})



  return (
    <div>
      <h3>Your email: {userData.email}</h3>
      <form
        className="login-form"
        action="submit"
        onSubmit={(e) => {
          e.preventDefault();
          console.log(e.target);
        }}
      >
        {renderInputForm}
        <button onClick={() => setIsLogin(true)}>LogIn</button>
      </form>
    </div>
  );
};

export default RegisterForm;
