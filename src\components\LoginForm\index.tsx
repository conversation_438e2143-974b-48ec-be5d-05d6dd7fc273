import "./index.scss";
import { useState } from "react";

interface IProps {
  setIsLogin: (val: boolean) => void;
}

const Form = ({ setIsLogin }: IProps) => {

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  return (
    <div>
      <h3>Your email: {email}</h3>
      <form
        className="login-form"
        action="submit"
        onSubmit={(e) => {
          e.preventDefault();
          console.log(e.target);
        }}
      >
        <div className="input-wrapper">
          <label htmlFor="email">Email : </label>
          <input
            type="email"
            name="email"  
            value={email}
            onChange={(e) => {
              console.log(e.target.value);
              setEmail(e.target.value);
            }}
          />
        </div>
        <div className="input-wrapper">
          <label htmlFor="password">Password : </label>
          <input
            type="password"
            name="password"
            id="password"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value);
            }}
          />
        </div>
        <button onClick={() => setIsLogin(true)}>LogIn</button>
      </form>
    </div>
  );
};

export default Form;
