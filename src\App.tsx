import NavBar from "./components/NavBar/index.tsx";
import { useState } from "react";
import RegisterForm from "./components/RegisterForm/index.tsx";
import UserDetails from "./components/UserDetails/index.tsx";
import type { IUserData } from "./interfaces/index.ts";

function App() {
  const campany = "Mario";
  const AboutUs = "About Us";
  const [isLogin, setIsLogin] = useState(false);
  const [userData, setUserData] = useState<IUserData>({
    username: "",
    email: "",
    password: "",
  });

  return (
    <>
      <NavBar
        companyName={campany}
        About={AboutUs}
        islogin={isLogin}
        setIsLogin={setIsLogin}
      />
      
      {isLogin ? (
        <UserDetails UserData={userData} />
      ) : (
        <RegisterForm
          setIsLogin={setIsLogin}
          setUserData={setUserData}
          userData={userData}
        />
      )}
    </>
  );
}

export default App;
